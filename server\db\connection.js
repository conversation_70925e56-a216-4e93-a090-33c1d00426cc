const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const { Pool } = require('pg');

// Create a new pool using the RDS host
const pool = new Pool({
  user: '<PERSON>',
  host: 'backupapril272025.cdc2o6m8gcel.us-west-2.rds.amazonaws.com',
  database: 'project_management',
  password: 'Rayne22!',
  port: 5432,
  ssl: {
    rejectUnauthorized: false
  }
});

// Test the connection
pool.connect((err, client, release) => {
  if (err) {
    console.error('Error connecting to the database:', err);
  } else {
    console.log('Successfully connected to database');
    release();
  }
});

module.exports = pool;
