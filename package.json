{"name": "project-management-app", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@mui/system": "^7.0.1", "@mui/x-date-pickers": "^7.28.2", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "fancy": "github:danie<PERSON><PERSON><PERSON>/fancy", "gantt-task-react": "^0.3.9", "jsonwebtoken": "^9.0.2", "pg": "^8.14.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-redux": "^9.2.0", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "ws": "^8.18.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "server": "node server/index.js", "server:dev": "nodemon server/index.js", "db:setup": "node server/db/setup.js", "db:migrate": "node server/db/run_specific_migration.js", "db:migrate:passwords": "node server/db/run_specific_migration.js update_user_passwords.sql", "db:test": "node server/db/test-connection.js", "db:migrate:data": "node server/db/one-time-scripts/migrate-data.js", "db:add-user": "node server/db/one-time-scripts/add-user.js", "db:add-task": "node server/db/one-time-scripts/add-task.js", "kill-port": "node server/utils/killPort.js", "dev": "concurrently \"npm run start\" \"npm run server:dev\"", "prod": "npm run build && cross-env NODE_ENV=production node server/index.js", "docs:update": "node update-docs.js", "docs:update:issues": "node update-docs.js --issues", "docs:update:dependencies": "node update-docs.js --dependencies"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/lodash": "^4.17.16", "@types/node": "^22.13.14", "@types/pg": "^8.11.11", "@types/react-beautiful-dnd": "^13.1.8", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^35.1.5", "electron-builder": "^26.0.12", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^4.9.5", "wait-on": "^8.0.3"}}