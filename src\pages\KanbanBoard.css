/* Kanban Board Styles */

/* Style for the body when dragging is active */
body.dragging-active {
  cursor: grabbing !important;
  user-select: none;
}

/* Add a pulsing effect to indicate draggability */
@keyframes pulse-border {
  0% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4); }
  70% { box-shadow: 0 0 0 5px rgba(33, 150, 243, 0); }
  100% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0); }
}

/* Drag image for custom drag preview */
.drag-image {
  position: absolute;
  top: -1000px;
  left: -1000px;
  background-color: #2196f3;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  pointer-events: none;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Style for droppable areas when being dragged over */
.drag-over {
  background-color: rgba(33, 150, 243, 0.15) !important;
  border: 2px dashed rgba(33, 150, 243, 0.5) !important;
  animation: pulse-border 1.5s infinite !important;
}

/* Smooth transitions for drag and drop */
.task-card-transition {
  transition: transform 0.2s, box-shadow 0.2s, opacity 0.2s;
}

/* Droppable area styles */
.droppable-area {
  transition: background-color 0.2s, border 0.2s;
  min-height: 100px;
}

/* Draggable item styles */
.draggable-item {
  transition: transform 0.2s, opacity 0.2s;
  position: relative;
  z-index: 1;
}

.draggable-item.is-dragging {
  z-index: 9999;
  pointer-events: auto !important;
  cursor: grabbing !important;
  opacity: 0.9;
  transform: scale(1.05);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Hover effect for task cards */
.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  cursor: grab;
  border-left: 3px solid #2196f3;
}

/* Dragging effect */
.task-card:active {
  cursor: grabbing;
  transform: scale(1.02);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Drag handle animation */
@keyframes wiggle {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.drag-handle-animation {
  animation: wiggle 1s ease-in-out infinite;
}

/* Make sure the drag handle is visible and obvious */
.drag-handle {
  cursor: grab !important;
  color: #2196f3 !important;
  transition: transform 0.2s;
}

.drag-handle:hover {
  transform: scale(1.2);
  color: #1976d2 !important;
}

.drag-handle-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  background-color: rgba(33, 150, 243, 0.2);
  transition: all 0.2s;
  animation: pulse-border 2s infinite;
  cursor: grab !important;
}

.drag-handle-container:hover {
  background-color: rgba(33, 150, 243, 0.4);
  transform: scale(1.1);
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.6);
}
