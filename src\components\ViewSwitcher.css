.ViewContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.ViewSwitcher {
  display: flex;
  align-items: center;
  gap: 10px;
}

.Button {
  padding: 8px 16px;
  border: 1px solid #ccc;
  background-color: #f8f8f8;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
}

.Button:hover {
  background-color: #e0e0e0;
}

.Switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.Switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.Slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.Slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
}

input:checked + .Slider {
  background-color: #2196F3;
}

input:focus + .Slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .Slider:before {
  transform: translateX(26px);
}

.Slider.round {
  border-radius: 34px;
}

.Slider.round:before {
  border-radius: 50%;
} 