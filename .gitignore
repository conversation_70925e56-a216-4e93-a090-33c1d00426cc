# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/dist
/app-exe
/ProjectManagementApp

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.development
.env.test
.env.production

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
server_log.txt

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.lnk
