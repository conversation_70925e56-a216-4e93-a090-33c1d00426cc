/* App container */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main container with sidebar and content */
.app-container {
  display: flex;
  flex: 1;
  margin-top: 64px; /* Height of the AppBar */
}

/* Main content area */
.main-content {
  flex-grow: 1;
  padding: 16px;
  transition: all 0.3s ease;
  width: 100%; /* Ensure it takes full width */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Adjust main content when sidebar is open */
.main-content.sidebar-open {
  padding-left: 16px; /* Keep consistent padding */
}

/* Adjust main content when sidebar is closed */
.main-content.sidebar-closed {
  margin-left: 0;
  padding-left: 16px; /* Reset to default padding */
  padding-right: 16px; /* Ensure consistent padding on both sides */
  max-width: 100%; /* Ensure it doesn't exceed the viewport width */
  width: 100%; /* Take up full width */
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .main-content.sidebar-open {
    margin-left: 0;
  }
}

/* Card styles */
.card {
  margin-bottom: 24px;
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* Status indicators */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-pending {
  background-color: #ffc107; /* Amber */
}

.status-in-progress {
  background-color: #2196f3; /* Blue */
}

.status-completed {
  background-color: #4caf50; /* Green */
}

.status-delayed {
  background-color: #f44336; /* Red */
}

.status-blocked {
  background-color: #9c27b0; /* Purple */
}

.status-cancelled {
  background-color: #9e9e9e; /* Grey */
}

.status-on-hold {
  background-color: #ff9800; /* Orange */
}

/* Priority indicators */
.priority-low {
  background-color: #8bc34a; /* Light Green */
}

.priority-medium {
  background-color: #ffc107; /* Amber */
}

.priority-high {
  background-color: #ff9800; /* Orange */
}

.priority-critical {
  background-color: #f44336; /* Red */
}

/* Progress bar */
.progress-bar-container {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin: 8px 0;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50; /* Green */
  transition: width 0.3s ease;
}

/* Table styles */
.table-container {
  overflow-x: auto;
}

/* Form styles */
.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.form-field {
  margin-bottom: 16px;
}

/* Dashboard widgets */
.dashboard-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-widget-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.dashboard-widget-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* Gantt chart styles */
.gantt-container {
  overflow-x: auto;
  min-height: 500px;
}

/* Kanban board styles */
.kanban-container {
  display: flex;
  overflow-x: auto;
  min-height: 600px;
  padding-bottom: 16px;
}

.kanban-column {
  min-width: 280px;
  margin-right: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.kanban-column-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.kanban-column-content {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.kanban-card {
  margin-bottom: 8px;
  cursor: pointer;
}

/* Calendar styles */
.calendar-container {
  min-height: 600px;
}

/* Utility classes */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.gap-8 {
  gap: 8px;
}

.gap-16 {
  gap: 16px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.p-0 {
  padding: 0;
}

.p-16 {
  padding: 16px;
}

.p-24 {
  padding: 24px;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.cursor-pointer {
  cursor: pointer;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

.text-italic {
  font-style: italic;
}

.text-uppercase {
  text-transform: uppercase;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}

.text-muted {
  color: #757575;
}

.text-primary {
  color: #1976d2;
}

.text-success {
  color: #4caf50;
}

.text-warning {
  color: #ff9800;
}

.text-danger {
  color: #f44336;
}

.text-info {
  color: #2196f3;
}

.bg-light {
  background-color: #f5f5f5;
}

.bg-white {
  background-color: #ffffff;
}

.bg-primary {
  background-color: #1976d2;
}

.bg-success {
  background-color: #4caf50;
}

.bg-warning {
  background-color: #ff9800;
}

.bg-danger {
  background-color: #f44336;
}

.bg-info {
  background-color: #2196f3;
}

.rounded {
  border-radius: 4px;
}

.rounded-circle {
  border-radius: 50%;
}

.shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.shadow-lg {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.border {
  border: 1px solid #e0e0e0;
}

.border-top {
  border-top: 1px solid #e0e0e0;
}

.border-bottom {
  border-bottom: 1px solid #e0e0e0;
}

.border-left {
  border-left: 1px solid #e0e0e0;
}

.border-right {
  border-right: 1px solid #e0e0e0;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-visible {
  overflow: visible;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

.d-flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.flex-grow-1 {
  flex-grow: 1;
}

.align-items-center {
  align-items: center;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.justify-content-evenly {
  justify-content: space-evenly;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-column-reverse {
  flex-direction: column-reverse;
}

.gap-8 {
  gap: 8px;
}

.gap-16 {
  gap: 16px;
}

.gap-24 {
  gap: 24px;
}

.gap-32 {
  gap: 32px;
}

.gap-40 {
  gap: 40px;
}

.gap-48 {
  gap: 48px;
}

.gap-56 {
  gap: 56px;
}

.gap-64 {
  gap: 64px;
}

.gap-72 {
  gap: 72px;
}

.gap-80 {
  gap: 80px;
}

.gap-88 {
  gap: 88px;
}

.gap-96 {
  gap: 96px;
}

.gap-104 {
  gap: 104px;
}

.gap-112 {
  gap: 112px;
}

.gap-120 {
  gap: 120px;
}

.gap-128 {
  gap: 128px;
}

.gap-136 {
  gap: 136px;
}

.gap-144 {
  gap: 144px;
}

.gap-152 {
  gap: 152px;
}

.gap-160 {
  gap: 160px;
}

.gap-168 {
  gap: 168px;
}

.gap-176 {
  gap: 176px;
}

.gap-184 {
  gap: 184px;
}

.gap-192 {
  gap: 192px;
}

.gap-200 {
  gap: 200px;
}

.gap-208 {
  gap: 208px;
}

.gap-216 {
  gap: 216px;
}

.gap-224 {
  gap: 224px;
}

.gap-232 {
  gap: 232px;
}

.gap-240 {
  gap: 240px;
}

.gap-248 {
  gap: 248px;
}

.gap-256 {
  gap: 256px;
}

.gap-264 {
  gap: 264px;
}

.gap-272 {
  gap: 272px;
}

.gap-280 {
  gap: 280px;
}

.gap-288 {
  gap: 288px;
}

.gap-296 {
  gap: 296px;
}

.gap-304 {
  gap: 304px;
}

.gap-312 {
  gap: 312px;
}

.gap-320 {
  gap: 320px;
}

.gap-328 {
  gap: 328px;
}

.gap-336 {
  gap: 336px;
}

.gap-344 {
  gap: 344px;
}

.gap-352 {
  gap: 352px;
}

.gap-360 {
  gap: 360px;
}

.gap-368 {
  gap: 368px;
}

.gap-376 {
  gap: 376px;
}

.gap-384 {
  gap: 384px;
}

.gap-392 {
  gap: 392px;
}

.gap-400 {
  gap: 400px;
}

.gap-408 {
  gap: 408px;
}

.gap-416 {
  gap: 416px;
}

.gap-424 {
  gap: 424px;
}

.gap-432 {
  gap: 432px;
}

.gap-440 {
  gap: 440px;
}

.gap-448 {
  gap: 448px;
}

.gap-456 {
  gap: 456px;
}

.gap-464 {
  gap: 464px;
}

.gap-472 {
  gap: 472px;
}

.gap-480 {
  gap: 480px;
}

.gap-488 {
  gap: 488px;
}

.gap-496 {
  gap: 496px;
}

.gap-504 {
  gap: 504px;
}

.gap-512 {
  gap: 512px;
}

.gap-520 {
  gap: 520px;
}

.gap-528 {
  gap: 528px;
}

.gap-536 {
  gap: 536px;
}

.gap-544 {
  gap: 544px;
}

.gap-552 {
  gap: 552px;
}

.gap-560 {
  gap: 560px;
}

.gap-568 {
  gap: 568px;
}

.gap-576 {
  gap: 576px;
}

.gap-584 {
  gap: 584px;
}

.gap-592 {
  gap: 592px;
}

.gap-600 {
  gap: 600px;
}

.gap-608 {
  gap: 608px;
}

.gap-616 {
  gap: 616px;
}

.gap-624 {
  gap: 624px;
}

.gap-632 {
  gap: 632px;
}

.gap-640 {
  gap: 640px;
}

.gap-648 {
  gap: 648px;
}

.gap-656 {
  gap: 656px;
}

.gap-664 {
  gap: 664px;
}

.gap-672 {
  gap: 672px;
}

.gap-680 {
  gap: 680px;
}

.gap-688 {
  gap: 688px;
}

.gap-696 {
  gap: 696px;
}

.gap-704 {
  gap: 704px;
}

.gap-712 {
  gap: 712px;
}

.gap-720 {
  gap: 720px;
}

.gap-728 {
  gap: 728px;
}

.gap-736 {
  gap: 736px;
}

.gap-744 {
  gap: 744px;
}

.gap-752 {
  gap: 752px;
}

.gap-760 {
  gap: 760px;
}

.gap-768 {
  gap: 768px;
}

.gap-776 {
  gap: 776px;
}

.gap-784 {
  gap: 784px;
}

.gap-792 {
  gap: 792px;
}

.gap-800 {
  gap: 800px;
}

.gap-808 {
  gap: 808px;
}

.gap-816 {
  gap: 816px;
}

.gap-824 {
  gap: 824px;
}

.gap-832 {
  gap: 832px;
}

.gap-840 {
  gap: 840px;
}

.gap-848 {
  gap: 848px;
}

.gap-856 {
  gap: 856px;
}

.gap-864 {
  gap: 864px;
}

.gap-872 {
  gap: 872px;
}

.gap-880 {
  gap: 880px;
}

.gap-888 {
  gap: 888px;
}

.gap-896 {
  gap: 896px;
}

.gap-904 {
  gap: 904px;
}

.gap-912 {
  gap: 912px;
}

.gap-920 {
  gap: 920px;
}

.gap-928 {
  gap: 928px;
}

.gap-936 {
  gap: 936px;
}

.gap-944 {
  gap: 944px;
}

.gap-952 {
  gap: 952px;
}

.gap-960 {
  gap: 960px;
}

.gap-968 {
  gap: 968px;
}

.gap-976 {
  gap: 976px;
}

.gap-984 {
  gap: 984px;
}

.gap-992 {
  gap: 992px;
}

.gap-1000 {
  gap: 1000px;
}

.gap-1008 {
  gap: 1008px;
}

.gap-1016 {
  gap: 1016px;
}

.gap-1024 {
  gap: 1024px;
}

.gap-1032 {
  gap: 1032px;
}

.gap-1040 {
  gap: 1040px;
}

.gap-1048 {
  gap: 1048px;
}

.gap-1056 {
  gap: 1056px;
}

.gap-1064 {
  gap: 1064px;
}

.gap-1072 {
  gap: 1072px;
}

.gap-1080 {
  gap: 1080px;
}

.gap-1088 {
  gap: 1088px;
}

.gap-1096 {
  gap: 1096px;
}

.gap-1104 {
  gap: 1104px;
}

.gap-1112 {
  gap: 1112px;
}

.gap-1120 {
  gap: 1120px;
}

.gap-1128 {
  gap: 1128px;
}

.gap-1136 {
  gap: 1136px;
}

.gap-1144 {
  gap: 1144px;
}

.gap-1152 {
  gap: 1152px;
}

.gap-1160 {
  gap: 1160px;
}

.gap-1168 {
  gap: 1168px;
}

.gap-1176 {
  gap: 1176px;
}

.gap-1184 {
  gap: 1184px;
}

.gap-1192 {
  gap: 1192px;
}

.gap-1200 {
  gap: 1200px;
}

.gap-1208 {
  gap: 1208px;
}

.gap-1216 {
  gap: 1216px;
}

.gap-1224 {
  gap: 1224px;
}

.gap-1232 {
  gap: 1232px;
}

.gap-1240 {
  gap: 1240px;
}

.gap-1248 {
  gap: 1248px;
}

.gap-1256 {
  gap: 1256px;
}

.gap-1264 {
  gap: 1264px;
}

.gap-1272 {
  gap: 1272px;
}

.gap-1280 {
  gap: 1280px;
}

.gap-1288 {
  gap: 1288px;
}

.gap-1296 {
  gap: 1296px;
}

.gap-1304 {
  gap: 1304px;
}

.gap-1312 {
  gap: 1312px;
}

.gap-1320 {
  gap: 1320px;
}

.gap-1328 {
  gap: 1328px;
}

.gap-1336 {
  gap: 1336px;
}

.gap-1344 {
  gap: 1344px;
}

.gap-1352 {
  gap: 1352px;
}

.gap-1360 {
  gap: 1360px;
}

.gap-1368 {
  gap: 1368px;
}

.gap-1376 {
  gap: 1376px;
}

.gap-1384 {
  gap: 1384px;
}

.gap-1392 {
  gap: 1392px;
}

.gap-1400 {
  gap: 1400px;
}

.gap-1408 {
  gap: 1408px;
}

.gap-1416 {
  gap: 1416px;
}

.gap-1424 {
  gap: 1424px;
}

.gap-1432 {
  gap: 1432px;
}

.gap-1440 {
  gap: 1440px;
}

.gap-1448 {
  gap: 1448px;
}

.gap-1456 {
  gap: 1456px;
}

.gap-1464 {
  gap: 1464px;
}

.gap-1472 {
  gap: 1472px;
}

.gap-1480 {
  gap: 1480px;
}

.gap-1488 {
  gap: 1488px;
}

.gap-1496 {
  gap: 1496px;
}

.gap-1504 {
  gap: 1504px;
}

.gap-1512 {
  gap: 1512px;
}

.gap-1520 {
  gap: 1520px;
}

.gap-1528 {
  gap: 1528px;
}

.gap-1536 {
  gap: 1536px;
}

.gap-1544 {
  gap: 1544px;
}

.gap-1552 {
  gap: 1552px;
}

.gap-1560 {
  gap: 1560px;
}

.gap-1568 {
  gap: 1568px;
}

.gap-1576 {
  gap: 1576px;
}

.gap-1584 {
  gap: 1584px;
}

.gap-1592 {
  gap: 1592px;
}

.gap-1600 {
  gap: 1600px;
}

.gap-1608 {
  gap: 1608px;
}

.gap-1616 {
  gap: 1616px;
}

.gap-1624 {
  gap: 1624px;
}

.gap-1632 {
  gap: 1632px;
}

.gap-1640 {
  gap: 1640px;
}

.gap-1648 {
  gap: 1648px;
}

.gap-1656 {
  gap: 1656px;
}

.gap-1664 {
  gap: 1664px;
}

.gap-1672 {
  gap: 1672px;
}

.gap-1680 {
  gap: 1680px;
}

.gap-1688 {
  gap: 1688px;
}

.gap-1696 {
  gap: 1696px;
}

.gap-1704 {
  gap: 1704px;
}

.gap-1712 {
  gap: 1712px;
}

.gap-1720 {
  gap: 1720px;
}

.gap-1728 {
  gap: 1728px;
}

.gap-1736 {
  gap: 1736px;
}

.gap-1744 {
  gap: 1744px;
}

.gap-1752 {
  gap: 1752px;
}

.gap-1760 {
  gap: 1760px;
}

.gap-1768 {
  gap: 1768px;
}

.gap-1776 {
  gap: 1776px;
}

.gap-1784 {
  gap: 1784px;
}

.gap-1792 {
  gap: 1792px;
}

.gap-1800 {
  gap: 1800px;
}

.gap-1808 {
  gap: 1808px;
}

.gap-1816 {
  gap: 1816px;
}

.gap-1824 {
  gap: 1824px;
}

.gap-1832 {
  gap: 1832px;
}

.gap-1840 {
  gap: 1840px;
}

.gap-1848 {
  gap: 1848px;
}

.gap-1856 {
  gap: 1856px;
}

.gap-1864 {
  gap: 1864px;
}

.gap-1872 {
  gap: 1872px;
}

.gap-1880 {
  gap: 1880px;
}

.gap-1888 {
  gap: 1888px;
}

.gap-1896 {
  gap: 1896px;
}

.gap-1904 {
  gap: 1904px;
}

.gap-1912 {
  gap: 1912px;
}

.gap-1920 {
  gap: 1920px;
}

.gap-1928 {
  gap: 1928px;
}

.gap-1936 {
  gap: 1936px;
}

.gap-1944 {
  gap: 1944px;
}

.gap-1952 {
  gap: 1952px;
}

.gap-1960 {
  gap: 1960px;
}

.gap-1968 {
  gap: 1968px;
}

.gap-1976 {
  gap: 1976px;
}

.gap-1984 {
  gap: 1984px;
}

.gap-1992 {
  gap: 1992px;
}

.gap-2000 {
}

/* Animation keyframes */
@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}
